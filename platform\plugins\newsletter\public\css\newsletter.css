.newsletter-popup .modal-dialog {
  background: var(--bs-modal-bg);
  border-radius: var(--bs-border-radius-lg);
  overflow: auto;
}
.newsletter-popup .modal-dialog .newsletter-popup-bg img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .newsletter-popup .modal-dialog .newsletter-popup-bg {
    width: 100%;
    height: 12rem;
  }
}
.newsletter-popup .modal-dialog .modal-content .newsletter-popup-content {
  width: 100%;
  padding: 1.25rem;
}
@media (min-width: 768px) {
  .newsletter-popup .modal-dialog .modal-content .newsletter-popup-content {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    padding: 4.5rem 3rem;
  }
}
.newsletter-popup .modal-dialog .modal-content .btn-close {
  top: 1rem;
  inset-inline-end: 1rem;
}
.newsletter-popup .modal-dialog .modal-content .modal-subtitle {
  letter-spacing: 1px;
  font-size: 15px;
}
.newsletter-popup .modal-dialog .modal-content .modal-text {
  font-size: 16px;
}
.newsletter-popup .modal-dialog .modal-content form label {
  font-size: 14px;
  font-weight: 500;
}
.newsletter-popup .modal-dialog .modal-content form label.required:after {
  content: " *";
  color: red;
}
.newsletter-popup .modal-dialog .modal-content form input[type=email] {
  height: 50px;
  padding: 0 16px;
}
.newsletter-popup .modal-dialog .modal-content form input:focus {
  box-shadow: none;
}
.newsletter-popup .modal-dialog .modal-content form button {
  --bs-btn-padding-y: 0.70rem;
  display: block;
  width: 100%;
  border-radius: var(--bs-border-radius) !important;
  margin-bottom: 1rem;
}
.newsletter-popup .modal-dialog .modal-content form .form-check {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.newsletter-popup .modal-dialog .modal-content form .form-check input {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0;
}
.newsletter-popup .modal-dialog .modal-content .button-loading {
  border: 1px solid #c4cdd5;
  cursor: default;
  text-shadow: none;
  color: transparent !important;
  position: relative;
  transition: border-color 0.2s ease-out;
}
.newsletter-popup .modal-dialog .modal-content .button-loading:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  border-width: 3px;
  border-style: solid;
  margin-top: -9px;
  margin-left: -9px;
  width: 18px;
  height: 18px;
  animation: button-loading-spinner 1s linear infinite;
  border-color: #ffffff;
  border-bottom-color: transparent;
}
.newsletter-popup .modal-dialog .modal-content .button-loading:hover, .newsletter-popup .modal-dialog .modal-content .button-loading:focus, .newsletter-popup .modal-dialog .modal-content .button-loading:active {
  color: transparent;
}
.newsletter-popup .modal-dialog .modal-content .newsletter-message {
  margin: 0 0 20px 0;
  padding: 15px 30px 15px 15px;
  border-left: 5px solid #eeeeee;
  border-radius: 0;
  display: none;
  width: 100%;
  font-size: 85%;
}
.newsletter-popup .modal-dialog .modal-content .newsletter-message.newsletter-success-message {
  background-color: #c0edf1;
  border-color: #58d0da;
  color: #000;
}
.newsletter-popup .modal-dialog .modal-content .newsletter-message.newsletter-error-message {
  background-color: #faeaa9;
  border-color: #f3cc31;
  color: #000;
}
@keyframes button-loading-spinner {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
