<section class="feature-area2 p-relative fix" style="background: #f7f5f1;">
    <?php if($floatingImage = $shortcode->right_floating_image): ?>
        <div class="animations-02">
            <img src="<?php echo e(RvMedia::getImageURL($floatingImage)); ?>" alt="<?php echo e($shortcode->title); ?>" />
        </div>
    <?php endif; ?>
    <div class="container">
        <div class="row justify-content-center align-items-center">
            <div class="col-lg-6 col-md-12 col-sm-12 pr-30">
                <?php if($leftImage = $shortcode->left_image): ?>
                    <div class="feature-img">
                        <img src="<?php echo e(RvMedia::getImageURL($leftImage)); ?>" alt="<?php echo e($shortcode->title); ?>" class="img" />
                    </div>
                <?php endif; ?>
            </div>
            <div class="col-lg-6 col-md-12 col-sm-12">
                <div class="feature-content s-about-content">
                    <?php if($shortcode->title || $shortcode->subtitle): ?>
                        <div class="feature-title pb-20">
                            <?php if($subtitle = $shortcode->subtitle): ?>
                                <h5><?php echo e($subtitle); ?></h5>
                            <?php endif; ?>

                            <?php if($title = $shortcode->title): ?>
                                <h2>
                                    <?php echo BaseHelper::clean($title); ?>

                                </h2>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <?php if($description = $shortcode->description): ?>
                        <p><?php echo BaseHelper::clean($description); ?></p>
                    <?php endif; ?>

                    <?php if($shortcode->button_label && $shortcode->button_url): ?>
                        <div class="slider-btn mt-15">
                            <a href="<?php echo e($shortcode->button_url); ?>" class="btn ss-btn smoth-scroll"><?php echo e($shortcode->button_label); ?></a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH G:\DL\app\app\platform\themes/riorelax/partials/shortcodes/services/index.blade.php ENDPATH**/ ?>